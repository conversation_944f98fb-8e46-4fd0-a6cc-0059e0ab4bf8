-- FRRadius Main Database Initialization
CREATE DATABASE IF NOT EXISTS frradius_main;
USE frradius_main;

-- Grant privileges
GRANT ALL PRIVILEGES ON frradius_main.* TO 'frradius_user'@'%';
FLUSH PRIVILEGES;

-- Create basic tables (Laravel migrations will handle the rest)
CREATE TABLE IF NOT EXISTS migrations (
    id int(10) unsigned NOT NULL AUTO_INCREMENT,
    migration varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    batch int(11) NOT NULL,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

# FreeRADIUS Configuration for FRRadius
prefix = /usr
exec_prefix = /usr
sysconfdir = /etc
localstatedir = /var
sbindir = /usr/sbin
logdir = /var/log/freeradius
raddbdir = /etc/freeradius/3.0
radacctdir = /var/log/freeradius/radacct

name = freeradius
confdir = ${raddbdir}
modconfdir = ${confdir}/mods-config
certdir = ${confdir}/certs
cadir = ${confdir}/certs
run_dir = /var/run/freeradius

db_dir = ${raddbdir}

libdir = /usr/lib/freeradius

pidfile = ${run_dir}/${name}.pid

correct_escapes = true

max_request_time = 30
cleanup_delay = 5
max_requests = 16384

hostname_lookups = no

log {
    destination = files
    colourise = yes
    file = ${logdir}/radius.log
    syslog_facility = daemon
    stripped_names = no
    auth = yes
    auth_badpass = no
    auth_goodpass = no
    msg_denied = "You are already logged in - access denied"
}

checkrad = ${sbindir}/checkrad

security {
    allow_core_dumps = no
    max_attributes = 200
    reject_delay = 1
    status_server = yes
}

proxy_requests = yes
$INCLUDE proxy.conf

client localhost {
    ipaddr = 127.0.0.1
    secret = testing123
    require_message_authenticator = no
    nas_type = other
}

client docker_network {
    ipaddr = **********/16
    secret = frradius
    require_message_authenticator = no
    nas_type = other
}

client mikrotik_routers {
    ipaddr = 0.0.0.0/0
    secret = frradius
    require_message_authenticator = no
    nas_type = mikrotik
}

listen {
    type = auth
    ipaddr = *
    port = 1812
    limit {
        max_connections = 16
        lifetime = 0
        idle_timeout = 30
    }
}

listen {
    type = acct
    ipaddr = *
    port = 1813
    limit {
        max_connections = 16
        lifetime = 0
        idle_timeout = 30
    }
}

listen {
    type = coa
    ipaddr = *
    port = 3799
    limit {
        max_connections = 16
        lifetime = 0
        idle_timeout = 30
    }
}

authorize {
    filter_username
    preprocess
    chap
    mschap
    digest
    suffix
    eap {
        ok = return
    }
    sql
    expiration
    logintime
    pap
}

authenticate {
    Auth-Type PAP {
        pap
    }
    Auth-Type CHAP {
        chap
    }
    Auth-Type MS-CHAP {
        mschap
    }
    digest
    eap
}

preacct {
    preprocess
    acct_unique
    suffix
    files
}

accounting {
    detail
    sql
    attr_filter.accounting_response
}

session {
    sql
}

post-auth {
    update {
        &reply: += &session-state:
    }
    sql
    remove_reply_message_if_eap
    Post-Auth-Type REJECT {
        attr_filter.access_reject
        eap
        remove_reply_message_if_eap
    }
    Post-Auth-Type Challenge {
    }
}

pre-proxy {
}

post-proxy {
    eap
}

$INCLUDE clients.conf
$INCLUDE sites-enabled/

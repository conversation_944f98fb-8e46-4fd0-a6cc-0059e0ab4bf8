sql {
    driver = "rlm_sql_mysql"
    dialect = "mysql"

    # Connection info:
    server = "mysql_radius"
    port = 3306
    login = "frradius_user"
    password = "frradius_password"

    # Database table configuration for everything except Oracle
    radius_db = "frradius_auth"

    # If you are using Oracle then use this instead
    # radius_db = "(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=localhost)(PORT=1521))(CONNECT_DATA=(SID=your_sid)))"

    acct_table1 = "radacct"
    acct_table2 = "radacct"
    postauth_table = "radpostauth"
    authcheck_table = "radcheck"
    groupcheck_table = "radgroupcheck"
    authreply_table = "radreply"
    groupreply_table = "radgroupreply"
    usergroup_table = "radusergroup"
    delete_stale_sessions = yes
    pool {
        start = ${thread[pool].start_servers}
        min = ${thread[pool].min_spare_servers}
        max = ${thread[pool].max_servers}
        spare = ${thread[pool].max_spare_servers}
        uses = 0
        retry_delay = 30
        lifetime = 0
        idle_timeout = 60
    }

    # Set to 'yes' to read radius clients from the database ('nas' table)
    # Clients will ONLY be read on server startup.
    read_clients = yes

    # Table to keep radius client info
    client_table = "nas"

    # The group attribute specific to this instance of rlm_sql
    group_attribute = "SQL-Group"

    # Read database-specific queries
    $INCLUDE ${modconfdir}/${.:name}/main/${dialect}/queries.conf
}

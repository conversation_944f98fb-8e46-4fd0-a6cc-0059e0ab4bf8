# FRRadius Docker Setup Guide

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed and running
- At least 4GB RAM available for containers
- Ports 80, 443, 1812, 1813, 3000, 3306, 3307, 6379 available

### Installation Steps

1. **<PERSON>lone and Setup**
   ```bash
   # Navigate to your FRRadius directory
   cd /path/to/frradius

   # Run the setup script
   ./docker-setup.sh
   ```

2. **Access Applications**
   - **Web Interface**: http://localhost
   - **PhpMyAdmin**: http://localhost:8080
   - **WhatsApp Gateway**: http://localhost:3000

## 🔧 Service Configuration

### Database Access
- **Main Database**: 
  - Host: localhost:3306
  - Database: frradius_main
  - User: frradius_user
  - Password: frradius_password

- **RADIUS Database**:
  - Host: localhost:3307
  - Database: frradius_auth
  - User: frradius_user
  - Password: frradius_password

### RADIUS Server
- **Authentication Port**: 1812/udp
- **Accounting Port**: 1813/udp
- **CoA/DM Port**: 3799/udp
- **Secret**: frradius

## 🌐 Network Configuration

### Docker Network
- **Subnet**: **********/16
- **Gateway**: **********

### Service IPs (Internal)
- nginx: **********
- php: **********
- mysql_main: **********
- mysql_radius: **********
- redis: **********
- freeradius: **********
- whatsapp_gateway: **********

## 🔌 OLT Configuration

### Untuk Akses OLT Fisik

1. **Dapatkan IP Host Docker Anda**
   ```bash
   # Pada macOS/Linux
   ifconfig | grep inet
   
   # Atau gunakan
   docker network inspect bridge
   ```

2. **Konfigurasi OLT**
   - **RADIUS Server IP**: [IP_HOST_DOCKER_ANDA]
   - **Auth Port**: 1812
   - **Acct Port**: 1813
   - **Secret**: frradius

### Contoh Konfigurasi OLT

#### ZTE OLT
```bash
# Masuk ke CLI ZTE OLT
configure terminal
radius-server host [IP_HOST_DOCKER] auth-port 1812 acct-port 1813 key frradius
aaa authentication ppp default radius
aaa accounting ppp default start-stop radius
```

#### Huawei OLT
```bash
# Masuk ke CLI Huawei OLT
system-view
radius-server template frradius
radius-server shared-key cipher frradius
radius-server authentication [IP_HOST_DOCKER] 1812
radius-server accounting [IP_HOST_DOCKER] 1813
```

## 🤖 Router/Mikrotik Configuration

### Mikrotik RouterOS
```bash
# Tambah RADIUS server
/radius
add address=[IP_HOST_DOCKER] secret=frradius service=ppp,hotspot,dhcp timeout=3000ms

# Enable RADIUS incoming
/radius incoming
set accept=yes port=3799
```

## 📱 WhatsApp Gateway Setup

1. **Akses QR Code**
   ```bash
   curl http://localhost:3000/qr
   ```

2. **Scan QR Code** dengan aplikasi WhatsApp di ponsel

3. **Cek Status**
   ```bash
   curl http://localhost:3000/status
   ```

4. **Test Kirim Pesan**
   ```bash
   curl -X POST http://localhost:3000/send-message \
     -H "Content-Type: application/json" \
     -d '{"number": "************", "message": "Test dari FRRadius"}'
   ```

## 🛠️ Troubleshooting

### Container Tidak Start
```bash
# Cek logs
docker-compose logs -f [service_name]

# Restart service
docker-compose restart [service_name]
```

### Database Connection Error
```bash
# Cek MySQL logs
docker-compose logs mysql_main
docker-compose logs mysql_radius

# Reset database
docker-compose down -v
docker-compose up -d
```

### RADIUS Not Working
```bash
# Test RADIUS
docker-compose exec freeradius radtest test test localhost 1812 testing123

# Cek RADIUS logs
docker-compose logs freeradius
```

### OLT Tidak Bisa Connect
1. Pastikan firewall tidak memblokir port 1812/1813
2. Cek IP host Docker sudah benar
3. Pastikan secret "frradius" sudah benar
4. Test dengan radtest dari OLT

## 🔐 Security Considerations

### Production Setup
1. **Ganti Secret Default**
   - Edit `docker/freeradius/raddb/clients.conf`
   - Ganti "frradius" dengan secret yang kuat

2. **Restrict Client Access**
   - Ganti `0.0.0.0/0` dengan IP range spesifik
   - Tambah client configuration per device

3. **Database Security**
   - Ganti password default di `.env`
   - Gunakan SSL untuk database connection

4. **Network Security**
   - Gunakan VPN untuk akses remote
   - Setup firewall rules yang proper

## 📊 Monitoring

### Health Checks
```bash
# Cek semua service
docker-compose ps

# Cek resource usage
docker stats

# Cek logs real-time
docker-compose logs -f
```

### Database Monitoring
- Akses PhpMyAdmin: http://localhost:8080
- Monitor RADIUS sessions di tabel `user_session`
- Monitor accounting di tabel `radacct`

## 🔄 Backup & Restore

### Backup Database
```bash
# Backup main database
docker-compose exec mysql_main mysqldump -u frradius_user -pfrradius_password frradius_main > backup_main.sql

# Backup RADIUS database
docker-compose exec mysql_radius mysqldump -u frradius_user -pfrradius_password frradius_auth > backup_radius.sql
```

### Restore Database
```bash
# Restore main database
docker-compose exec -T mysql_main mysql -u frradius_user -pfrradius_password frradius_main < backup_main.sql

# Restore RADIUS database
docker-compose exec -T mysql_radius mysql -u frradius_user -pfrradius_password frradius_auth < backup_radius.sql
```

## 📞 Support

Jika mengalami masalah:
1. Cek logs dengan `docker-compose logs -f`
2. Pastikan semua port tidak bentrok
3. Restart Docker Desktop jika perlu
4. Cek dokumentasi FreeRADIUS untuk konfigurasi advanced

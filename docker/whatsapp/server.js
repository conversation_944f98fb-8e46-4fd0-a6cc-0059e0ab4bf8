const express = require('express');
const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode');
const cors = require('cors');
const bodyParser = require('body-parser');
const multer = require('multer');
const fs = require('fs');
const path = require('path');

const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Multer configuration for file uploads
const upload = multer({ dest: 'uploads/' });

// WhatsApp Client
let client;
let qrCodeData = '';
let isReady = false;
let clientInfo = {};

// Initialize WhatsApp Client
function initializeClient() {
    client = new Client({
        authStrategy: new LocalAuth({
            dataPath: './sessions'
        }),
        puppeteer: {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-gpu'
            ]
        }
    });

    client.on('qr', (qr) => {
        console.log('QR Code received');
        qrCodeData = qr;
        qrcode.toDataURL(qr, (err, url) => {
            if (err) {
                console.error('Error generating QR code:', err);
            } else {
                qrCodeData = url;
            }
        });
    });

    client.on('ready', () => {
        console.log('WhatsApp Client is ready!');
        isReady = true;
        clientInfo = {
            user: client.info,
            battery: client.info.battery,
            platform: client.info.platform
        };
    });

    client.on('authenticated', () => {
        console.log('WhatsApp Client authenticated');
    });

    client.on('auth_failure', (msg) => {
        console.error('Authentication failed:', msg);
    });

    client.on('disconnected', (reason) => {
        console.log('WhatsApp Client disconnected:', reason);
        isReady = false;
        qrCodeData = '';
        clientInfo = {};
    });

    client.on('message', async (message) => {
        console.log('Message received:', message.body);
        // Handle incoming messages here if needed
    });

    client.initialize();
}

// Routes
app.get('/', (req, res) => {
    res.json({
        status: 'success',
        message: 'FRRadius WhatsApp Gateway is running',
        ready: isReady,
        info: clientInfo
    });
});

app.get('/qr', (req, res) => {
    if (qrCodeData) {
        res.json({
            status: 'success',
            qr: qrCodeData
        });
    } else {
        res.json({
            status: 'error',
            message: 'QR Code not available'
        });
    }
});

app.get('/status', (req, res) => {
    res.json({
        status: 'success',
        ready: isReady,
        info: clientInfo
    });
});

app.post('/send-message', async (req, res) => {
    if (!isReady) {
        return res.status(400).json({
            status: 'error',
            message: 'WhatsApp client is not ready'
        });
    }

    const { number, message } = req.body;

    if (!number || !message) {
        return res.status(400).json({
            status: 'error',
            message: 'Number and message are required'
        });
    }

    try {
        const chatId = number.includes('@c.us') ? number : `${number}@c.us`;
        await client.sendMessage(chatId, message);
        
        res.json({
            status: 'success',
            message: 'Message sent successfully'
        });
    } catch (error) {
        console.error('Error sending message:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to send message',
            error: error.message
        });
    }
});

app.post('/send-media', upload.single('media'), async (req, res) => {
    if (!isReady) {
        return res.status(400).json({
            status: 'error',
            message: 'WhatsApp client is not ready'
        });
    }

    const { number, caption } = req.body;
    const file = req.file;

    if (!number || !file) {
        return res.status(400).json({
            status: 'error',
            message: 'Number and media file are required'
        });
    }

    try {
        const chatId = number.includes('@c.us') ? number : `${number}@c.us`;
        const media = MessageMedia.fromFilePath(file.path);
        
        await client.sendMessage(chatId, media, { caption: caption || '' });
        
        // Clean up uploaded file
        fs.unlinkSync(file.path);
        
        res.json({
            status: 'success',
            message: 'Media sent successfully'
        });
    } catch (error) {
        console.error('Error sending media:', error);
        
        // Clean up uploaded file on error
        if (file && fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
        }
        
        res.status(500).json({
            status: 'error',
            message: 'Failed to send media',
            error: error.message
        });
    }
});

app.post('/broadcast', async (req, res) => {
    if (!isReady) {
        return res.status(400).json({
            status: 'error',
            message: 'WhatsApp client is not ready'
        });
    }

    const { numbers, message } = req.body;

    if (!numbers || !Array.isArray(numbers) || !message) {
        return res.status(400).json({
            status: 'error',
            message: 'Numbers array and message are required'
        });
    }

    try {
        const results = [];
        
        for (const number of numbers) {
            try {
                const chatId = number.includes('@c.us') ? number : `${number}@c.us`;
                await client.sendMessage(chatId, message);
                results.push({ number, status: 'success' });
                
                // Add delay between messages to avoid being blocked
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                results.push({ number, status: 'error', error: error.message });
            }
        }
        
        res.json({
            status: 'success',
            message: 'Broadcast completed',
            results: results
        });
    } catch (error) {
        console.error('Error in broadcast:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to send broadcast',
            error: error.message
        });
    }
});

app.post('/restart', (req, res) => {
    try {
        if (client) {
            client.destroy();
        }
        
        setTimeout(() => {
            initializeClient();
        }, 2000);
        
        res.json({
            status: 'success',
            message: 'WhatsApp client restarting'
        });
    } catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Failed to restart client',
            error: error.message
        });
    }
});

// Start server
app.listen(port, () => {
    console.log(`WhatsApp Gateway server running on port ${port}`);
    initializeClient();
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down gracefully...');
    if (client) {
        await client.destroy();
    }
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('Shutting down gracefully...');
    if (client) {
        await client.destroy();
    }
    process.exit(0);
});

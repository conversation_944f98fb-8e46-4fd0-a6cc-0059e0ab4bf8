[mysqld]
# Basic settings for RADIUS database
default-storage-engine = innodb
innodb_file_per_table = 1
innodb_flush_log_at_trx_commit = 1
innodb_log_buffer_size = 16M
innodb_buffer_pool_size = 128M
innodb_log_file_size = 64M
innodb_thread_concurrency = 8
innodb_autoextend_increment = 32
innodb_buffer_pool_instances = 4
innodb_concurrency_tickets = 5000
innodb_old_blocks_time = 1000
innodb_stats_on_metadata = 0

# Connection settings
max_connections = 100
max_connect_errors = 1000000
max_allowed_packet = 64M
interactive_timeout = 1800
wait_timeout = 1800

# Query cache
query_cache_type = 1
query_cache_size = 32M
query_cache_limit = 1M

# Character set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Timezone
default-time-zone = '+07:00'

# Binary logging
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 3

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4

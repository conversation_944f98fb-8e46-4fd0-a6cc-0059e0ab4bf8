# 🚀 FRRadius - Complete RADIUS Management System

FRRadius adalah aplikasi manajemen RADIUS server yang komprehensif, dibangun dengan Laravel untuk mengelola autentikasi dan akuntansi jaringan. Sistem ini mendukung integrasi dengan berbagai perangkat jaringan seperti OLT, <PERSON>r Mi<PERSON>rotik, dan dilen<PERSON><PERSON><PERSON> dengan WhatsApp Gateway untuk notifikasi.

## ✨ Fitur Utama

-   🔐 **RADIUS Authentication & Accounting** - FreeRADIUS terintegrasi
-   🌐 **Multi-Vendor OLT Support** - ZTE, Huawei, Fiberhome, VSOL, BDCOM
-   🤖 **Mikrotik Integration** - PPPoE, Hotspot, VPN management
-   📱 **WhatsApp Gateway** - Notifikasi otomatis dan broadcast
-   💾 **Dual Database** - MySQL untuk aplikasi dan RADIUS
-   📊 **Real-time Monitoring** - Session tracking dan bandwidth monitoring
-   🏢 **Multi-tenant** - Support multiple ISP/provider
-   💰 **Billing Integration** - Invoice dan payment gateway
-   📈 **Analytics & Reporting** - Dashboard dan laporan lengkap

## 🐳 Docker Installation (Recommended)

### Quick Start

```bash
# Clone repository
git clone [repository-url]
cd frradius

# Run setup script
./docker-setup.sh
```

### Manual Docker Setup

```bash
# Build and start containers
docker-compose up -d

# Setup Laravel
docker-compose exec php composer install
docker-compose exec php php artisan migrate
docker-compose exec php php artisan key:generate
```

## 🌐 Access Points

-   **Web Interface**: http://localhost
-   **PhpMyAdmin**: http://localhost:8080
-   **WhatsApp Gateway**: http://localhost:3000
-   **RADIUS Server**: localhost:1812 (auth), localhost:1813 (acct)

## 📋 System Requirements

### Docker Requirements

-   Docker Desktop 4.0+
-   4GB RAM minimum
-   10GB disk space
-   Ports: 80, 443, 1812, 1813, 3000, 3306, 3307, 6379

### Traditional Installation

-   PHP 8.2+
-   MySQL 8.0+
-   Redis 6.0+
-   FreeRADIUS 3.0+
-   Node.js 18+ (untuk WhatsApp Gateway)

## 🔧 Docker Management

### Available Commands

```bash
# Start services
./docker-manage.sh start

# Stop services
./docker-manage.sh stop

# View logs
./docker-manage.sh logs -f

# Access shell
./docker-manage.sh shell

# Backup databases
./docker-manage.sh backup

# Show status
./docker-manage.sh status
```

## 🔌 OLT Configuration

### Supported OLT Vendors

-   **ZTE** (C300, C320, C600, dll)
-   **Huawei** (MA5608T, MA5683T, dll)
-   **Fiberhome** (AN5516, AN5506, dll)
-   **VSOL** (V1600D, V2400G, dll)
-   **BDCOM** (P3310C, P3608, dll)

### Quick OLT Setup

1. Dapatkan IP host Docker Anda
2. Konfigurasi RADIUS di OLT:
    - **Server**: [IP_HOST_DOCKER]
    - **Auth Port**: 1812
    - **Acct Port**: 1813
    - **Secret**: frradius

📖 **Detail**: Lihat [OLT_CONFIGURATION_GUIDE.md](OLT_CONFIGURATION_GUIDE.md)

## 📱 WhatsApp Gateway

### Setup WhatsApp

1. Akses http://localhost:3000/qr
2. Scan QR code dengan WhatsApp mobile
3. Cek status di http://localhost:3000/status

### API Endpoints

```bash
# Send message
POST /send-message
{
  "number": "************",
  "message": "Hello from FRRadius"
}

# Broadcast message
POST /broadcast
{
  "numbers": ["************", "************"],
  "message": "Broadcast message"
}
```

## 🛠️ Development

### Local Development

```bash
# Install dependencies
composer install
npm install

# Setup environment
cp .env.example .env
php artisan key:generate

# Run migrations
php artisan migrate

# Start development server
php artisan serve
npm run dev
```

### Testing

```bash
# Run tests
php artisan test

# Run specific test
php artisan test --filter=RadiusTest
```

## 📊 Monitoring & Logs

### Service Monitoring

```bash
# Check all services
docker-compose ps

# Monitor resources
docker stats

# View specific logs
docker-compose logs -f freeradius
docker-compose logs -f whatsapp_gateway
```

### Database Monitoring

-   **PhpMyAdmin**: http://localhost:8080
-   **Main DB**: frradius_main
-   **RADIUS DB**: frradius_auth

## 🔐 Security

### Production Checklist

-   [ ] Change default RADIUS secret
-   [ ] Restrict client IP ranges
-   [ ] Update database passwords
-   [ ] Enable SSL/TLS
-   [ ] Setup firewall rules
-   [ ] Regular security updates

## 📚 Documentation

-   [Docker Setup Guide](DOCKER_SETUP_GUIDE.md)
-   [OLT Configuration Guide](OLT_CONFIGURATION_GUIDE.md)
-   [API Documentation](docs/api.md)
-   [Troubleshooting Guide](docs/troubleshooting.md)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

-   📧 Email: <EMAIL>
-   💬 Discord: [FRRadius Community](https://discord.gg/frradius)
-   📖 Wiki: [GitHub Wiki](https://github.com/frradius/frradius/wiki)
-   🐛 Issues: [GitHub Issues](https://github.com/frradius/frradius/issues)

---

**⭐ Jika project ini membantu, jangan lupa berikan star di GitHub!**

# 🔌 OLT Configuration Guide untuk FRRadius Docker

## 📋 Overview

Panduan ini menjelaskan cara mengkonfigurasi berbagai jenis OLT untuk bekerja dengan FRRadius yang berjalan di Docker Desktop.

## 🌐 Network Setup

### 1. Dapatkan IP Host Docker Anda

**Pada macOS:**
```bash
# Cek IP interface aktif
ifconfig en0 | grep inet
# atau
ipconfig getifaddr en0
```

**Pada Windows:**
```cmd
ipconfig | findstr IPv4
```

**Pada Linux:**
```bash
ip addr show | grep inet
```

### 2. Test Koneksi ke RADIUS Server

```bash
# Test dari host
telnet [IP_HOST_ANDA] 1812

# Test RADIUS authentication (install freeradius-utils)
radtest testuser testpass [IP_HOST_ANDA] 1812 frradius
```

## 🏭 Konfigurasi OLT Berdasarkan Vendor

### ZTE OLT (C300, C320, dll)

#### Via CLI:
```bash
# Login ke OLT
telnet [IP_OLT]

# Masuk ke configuration mode
configure terminal

# Tambah RADIUS server
radius-server host [IP_HOST_DOCKER] auth-port 1812 acct-port 1813 key frradius

# Set authentication method
aaa authentication ppp default radius local
aaa accounting ppp default start-stop radius

# Apply configuration
commit
exit
```

#### Via Web Interface:
1. Login ke web interface OLT
2. Navigate ke **System > AAA > RADIUS**
3. Add RADIUS Server:
   - **Server IP**: [IP_HOST_DOCKER]
   - **Auth Port**: 1812
   - **Acct Port**: 1813
   - **Shared Key**: frradius
   - **Timeout**: 5 seconds
   - **Retries**: 3

### Huawei OLT (MA5608T, MA5683T, dll)

#### Via CLI:
```bash
# Login ke OLT
telnet [IP_OLT]

# Masuk ke system view
system-view

# Create RADIUS server template
radius-server template frradius
radius-server shared-key cipher frradius
radius-server authentication [IP_HOST_DOCKER] 1812
radius-server accounting [IP_HOST_DOCKER] 1813
radius-server retransmit 3
radius-server timeout 5
quit

# Apply to domain
domain default
authentication-scheme radius
accounting-scheme radius
radius-server frradius
quit

# Save configuration
save
```

### Fiberhome OLT (AN5516, AN5506, dll)

#### Via CLI:
```bash
# Login ke OLT
telnet [IP_OLT]

# Enter configuration mode
config

# Configure RADIUS
radius-server host [IP_HOST_DOCKER] auth-port 1812 acct-port 1813 key frradius timeout 5 retransmit 3

# Set AAA
aaa authentication ppp default radius
aaa accounting ppp default start-stop radius

# Apply configuration
commit
end
```

### VSOL OLT (V1600D, V2400G, dll)

#### Via Web Interface:
1. Login ke web management
2. Go to **System > User Management > RADIUS**
3. Configure:
   - **Primary RADIUS Server**: [IP_HOST_DOCKER]
   - **Auth Port**: 1812
   - **Acct Port**: 1813
   - **Shared Secret**: frradius
   - **Timeout**: 5
   - **Max Retries**: 3

### BDCOM OLT (P3310C, P3608, dll)

#### Via CLI:
```bash
# Login via telnet/SSH
telnet [IP_OLT]

# Enter configuration mode
configure

# Add RADIUS server
radius-server host [IP_HOST_DOCKER] auth-port 1812 acct-port 1813 key frradius

# Configure AAA
aaa authentication ppp default radius
aaa accounting ppp default start-stop radius

# Save configuration
write memory
```

## 🔧 Konfigurasi Advanced

### 1. Multiple RADIUS Servers (Redundancy)

```bash
# Primary RADIUS server
radius-server host [IP_HOST_DOCKER] auth-port 1812 acct-port 1813 key frradius

# Secondary RADIUS server (jika ada)
radius-server host [IP_BACKUP_SERVER] auth-port 1812 acct-port 1813 key frradius
```

### 2. RADIUS Attributes untuk Bandwidth Control

Tambahkan di database RADIUS (tabel `radreply`):

```sql
-- Contoh user dengan bandwidth 10Mbps
INSERT INTO radreply (username, attribute, op, value) VALUES 
('user001', 'Mikrotik-Rate-Limit', '=', '10M/10M'),
('user001', 'Framed-IP-Address', '=', '**************');
```

### 3. CoA/DM Configuration (Change of Authorization)

```bash
# Enable CoA pada OLT
radius-server host [IP_HOST_DOCKER] coa-port 3799 key frradius

# Test CoA disconnect
echo "User-Name=user001" | radclient [IP_OLT]:3799 disconnect frradius
```

## 🧪 Testing & Troubleshooting

### 1. Test RADIUS dari OLT

```bash
# Test authentication
test radius-server [IP_HOST_DOCKER] username testuser password testpass

# Test accounting
test radius-accounting [IP_HOST_DOCKER] username testuser
```

### 2. Monitor RADIUS Logs

```bash
# Dari host Docker
docker-compose logs -f freeradius

# Atau akses container
docker-compose exec freeradius tail -f /var/log/freeradius/radius.log
```

### 3. Debug RADIUS

```bash
# Enable debug mode
docker-compose exec freeradius freeradius -X

# Test dengan radtest
radtest testuser testpass [IP_HOST_DOCKER] 1812 frradius
```

### 4. Common Issues & Solutions

#### Issue: OLT tidak bisa connect ke RADIUS
**Solution:**
1. Cek firewall host Docker
2. Pastikan port 1812/1813 tidak diblokir
3. Verify IP host Docker benar
4. Test dengan telnet

#### Issue: Authentication failed
**Solution:**
1. Cek secret key sama di OLT dan RADIUS
2. Verify user ada di database
3. Cek RADIUS logs untuk error detail

#### Issue: Accounting tidak jalan
**Solution:**
1. Pastikan port 1813 terbuka
2. Cek konfigurasi accounting di OLT
3. Verify tabel radacct di database

## 📊 Monitoring & Maintenance

### 1. Database Monitoring

```sql
-- Cek active sessions
SELECT * FROM user_session WHERE stop_time IS NULL;

-- Cek accounting records
SELECT * FROM radacct ORDER BY acctstarttime DESC LIMIT 10;

-- Cek authentication logs
SELECT * FROM radpostauth ORDER BY authdate DESC LIMIT 10;
```

### 2. Performance Monitoring

```bash
# Monitor container resources
docker stats

# Monitor RADIUS performance
docker-compose exec freeradius radmin -e "stats client"
```

### 3. Backup Configuration

```bash
# Backup OLT configuration (contoh untuk ZTE)
# Via TFTP atau SCP sesuai vendor OLT

# Backup RADIUS database
./docker-manage.sh backup
```

## 🔐 Security Best Practices

### 1. Change Default Secrets
```bash
# Ganti secret default di clients.conf
# Gunakan secret yang kuat dan unik per OLT
```

### 2. Restrict Client Access
```bash
# Ganti 0.0.0.0/0 dengan IP range spesifik
client olt_network {
    ipaddr = ***********/24
    secret = your_strong_secret_here
    nas_type = other
}
```

### 3. Enable Logging
```bash
# Monitor semua authentication attempts
# Set up log rotation
# Enable audit logging
```

### 4. Network Security
- Gunakan VPN untuk management access
- Implement proper firewall rules
- Regular security updates
- Monitor for suspicious activities

## 📞 Support & Troubleshooting

Jika mengalami masalah:

1. **Cek logs**: `docker-compose logs -f freeradius`
2. **Test connectivity**: `telnet [IP_HOST] 1812`
3. **Verify configuration**: Compare dengan contoh di atas
4. **Check database**: Pastikan user dan NAS terdaftar
5. **Network issues**: Cek firewall dan routing

Untuk bantuan lebih lanjut, dokumentasikan:
- Model dan versi OLT
- Error messages dari logs
- Network topology
- Configuration yang sudah dicoba

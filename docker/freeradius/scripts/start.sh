#!/bin/bash

# Wait for MySQL to be ready
echo "Waiting for MySQL to be ready..."
while ! mysql -h${MYSQL_HOST} -P${MYSQL_PORT} -u${MYSQL_USERNAME} -p${MYSQL_PASSWORD} -e "SELECT 1" >/dev/null 2>&1; do
    echo "MySQL is not ready yet. Waiting..."
    sleep 5
done

echo "MySQL is ready. Starting FreeRADIUS..."

# Update SQL configuration with environment variables
sed -i "s/server = \"localhost\"/server = \"${MYSQL_HOST}\"/" /etc/freeradius/3.0/mods-available/sql
sed -i "s/port = 3306/port = ${MYSQL_PORT}/" /etc/freeradius/3.0/mods-available/sql
sed -i "s/login = \"radius\"/login = \"${MYSQL_USERNAME}\"/" /etc/freeradius/3.0/mods-available/sql
sed -i "s/password = \"radpass\"/password = \"${MYSQL_PASSWORD}\"/" /etc/freeradius/3.0/mods-available/sql
sed -i "s/radius_db = \"radius\"/radius_db = \"${MYSQL_DATABASE}\"/" /etc/freeradius/3.0/mods-available/sql

# Enable SQL module
ln -sf /etc/freeradius/3.0/mods-available/sql /etc/freeradius/3.0/mods-enabled/sql

# Start FreeRADIUS in foreground
exec freeradius -X

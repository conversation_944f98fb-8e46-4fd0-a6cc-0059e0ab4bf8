#!/bin/bash

# FRRadius Docker Setup Script
echo "🚀 Setting up FRRadius with Docker..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p docker/freeradius/logs
mkdir -p docker/whatsapp/sessions
mkdir -p storage/logs
mkdir -p storage/app/public

# Set permissions
chmod -R 755 docker/
chmod +x docker/freeradius/scripts/start.sh

# Copy environment file for Docker
if [ ! -f .env ]; then
    echo "📋 Copying Docker environment configuration..."
    cp .env.docker .env
else
    echo "⚠️  .env file already exists. Please check your configuration."
fi

# Build and start containers
echo "🔨 Building Docker containers..."
docker-compose build --no-cache

echo "🚀 Starting FRRadius services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check if services are running
echo "🔍 Checking service status..."
docker-compose ps

# Run Laravel setup
echo "🔧 Setting up Laravel application..."
docker-compose exec php composer install --no-dev --optimize-autoloader
docker-compose exec php php artisan key:generate --force
docker-compose exec php php artisan config:cache
docker-compose exec php php artisan route:cache
docker-compose exec php php artisan view:cache

# Run database migrations
echo "💾 Running database migrations..."
docker-compose exec php php artisan migrate --force

# Set proper permissions
echo "🔐 Setting proper permissions..."
docker-compose exec php chown -R www-data:www-data /var/www/html/storage
docker-compose exec php chown -R www-data:www-data /var/www/html/bootstrap/cache

echo ""
echo "✅ FRRadius Docker setup completed!"
echo ""
echo "🌐 Access your application:"
echo "   - Web Interface: http://localhost"
echo "   - PhpMyAdmin: http://localhost:8080"
echo "   - WhatsApp Gateway: http://localhost:3000"
echo ""
echo "📊 Service Ports:"
echo "   - Web (HTTP): 80"
echo "   - Web (HTTPS): 443"
echo "   - MySQL Main: 3306"
echo "   - MySQL RADIUS: 3307"
echo "   - Redis: 6379"
echo "   - RADIUS Auth: 1812/udp"
echo "   - RADIUS Acct: 1813/udp"
echo "   - RADIUS CoA: 3799/udp"
echo "   - WhatsApp Gateway: 3000"
echo ""
echo "🔧 Useful commands:"
echo "   - View logs: docker-compose logs -f"
echo "   - Stop services: docker-compose down"
echo "   - Restart services: docker-compose restart"
echo "   - Access PHP container: docker-compose exec php bash"
echo ""
echo "📱 WhatsApp Gateway Setup:"
echo "   1. Visit http://localhost:3000/qr to get QR code"
echo "   2. Scan with your WhatsApp mobile app"
echo "   3. Check status at http://localhost:3000/status"
echo ""
echo "🔌 OLT Configuration:"
echo "   - OLT devices can connect to RADIUS server at your Docker host IP"
echo "   - Use port 1812 for authentication and 1813 for accounting"
echo "   - RADIUS secret: frradius"
echo ""
echo "⚠️  Important Notes:"
echo "   - Make sure to configure your OLT devices to point to this server"
echo "   - Update your router/mikrotik configurations"
echo "   - Check firewall settings if having connection issues"
echo ""

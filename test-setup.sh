#!/bin/bash

# FRRadius Docker Test Script
echo "🧪 Testing FRRadius Docker Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test functions
test_docker() {
    echo -n "🐳 Testing Docker... "
    if command -v docker &> /dev/null; then
        if docker info &> /dev/null; then
            echo -e "${GREEN}✅ Docker is running${NC}"
            return 0
        else
            echo -e "${RED}❌ Docker is not running${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ Docker is not installed${NC}"
        return 1
    fi
}

test_docker_compose() {
    echo -n "🔧 Testing Docker Compose... "
    if command -v docker-compose &> /dev/null; then
        echo -e "${GREEN}✅ Docker Compose is available${NC}"
        return 0
    else
        echo -e "${RED}❌ Docker Compose is not installed${NC}"
        return 1
    fi
}

test_ports() {
    echo "🔌 Testing port availability..."
    ports=(80 443 1812 1813 3000 3306 3307 6379)
    
    for port in "${ports[@]}"; do
        echo -n "   Port $port... "
        if lsof -i :$port &> /dev/null; then
            echo -e "${YELLOW}⚠️  Port $port is in use${NC}"
        else
            echo -e "${GREEN}✅ Available${NC}"
        fi
    done
}

test_files() {
    echo "📁 Testing required files..."
    files=(
        "docker-compose.yml"
        "docker/php/Dockerfile"
        "docker/nginx/nginx.conf"
        "docker/freeradius/Dockerfile"
        "docker/whatsapp/Dockerfile"
        ".env.docker"
    )
    
    for file in "${files[@]}"; do
        echo -n "   $file... "
        if [ -f "$file" ]; then
            echo -e "${GREEN}✅ Found${NC}"
        else
            echo -e "${RED}❌ Missing${NC}"
        fi
    done
}

test_directories() {
    echo "📂 Testing required directories..."
    dirs=(
        "docker/php"
        "docker/nginx"
        "docker/freeradius"
        "docker/whatsapp"
        "docker/mysql"
    )
    
    for dir in "${dirs[@]}"; do
        echo -n "   $dir... "
        if [ -d "$dir" ]; then
            echo -e "${GREEN}✅ Found${NC}"
        else
            echo -e "${RED}❌ Missing${NC}"
        fi
    done
}

test_permissions() {
    echo "🔐 Testing file permissions..."
    scripts=(
        "docker-setup.sh"
        "docker-manage.sh"
        "docker/freeradius/scripts/start.sh"
    )
    
    for script in "${scripts[@]}"; do
        echo -n "   $script... "
        if [ -x "$script" ]; then
            echo -e "${GREEN}✅ Executable${NC}"
        else
            echo -e "${YELLOW}⚠️  Not executable (will fix)${NC}"
            chmod +x "$script" 2>/dev/null
        fi
    done
}

test_environment() {
    echo "🌍 Testing environment configuration..."
    echo -n "   .env file... "
    if [ -f ".env" ]; then
        echo -e "${GREEN}✅ Found${NC}"
    else
        echo -e "${YELLOW}⚠️  Not found (will use .env.docker)${NC}"
        if [ -f ".env.docker" ]; then
            cp .env.docker .env
            echo -e "${GREEN}   ✅ Copied from .env.docker${NC}"
        fi
    fi
}

test_docker_build() {
    echo "🔨 Testing Docker build (dry run)..."
    echo -n "   Validating docker-compose.yml... "
    if docker-compose config &> /dev/null; then
        echo -e "${GREEN}✅ Valid configuration${NC}"
    else
        echo -e "${RED}❌ Invalid configuration${NC}"
        echo "Error details:"
        docker-compose config
        return 1
    fi
}

show_system_info() {
    echo ""
    echo "💻 System Information:"
    echo "   OS: $(uname -s)"
    echo "   Architecture: $(uname -m)"
    echo "   Docker Version: $(docker --version 2>/dev/null || echo 'Not installed')"
    echo "   Docker Compose Version: $(docker-compose --version 2>/dev/null || echo 'Not installed')"
    echo "   Available Memory: $(free -h 2>/dev/null | grep Mem | awk '{print $7}' || echo 'Unknown')"
    echo "   Available Disk: $(df -h . | tail -1 | awk '{print $4}')"
}

show_next_steps() {
    echo ""
    echo "🚀 Next Steps:"
    echo "   1. Run: ./docker-setup.sh"
    echo "   2. Wait for all services to start"
    echo "   3. Access web interface: http://localhost"
    echo "   4. Setup WhatsApp: http://localhost:3000/qr"
    echo "   5. Configure your OLT devices"
    echo ""
    echo "📚 Documentation:"
    echo "   - Setup Guide: DOCKER_SETUP_GUIDE.md"
    echo "   - OLT Config: OLT_CONFIGURATION_GUIDE.md"
    echo "   - Management: ./docker-manage.sh help"
}

# Main test execution
echo "========================================"
echo "🧪 FRRadius Docker Setup Test"
echo "========================================"

# Run all tests
test_docker || exit 1
test_docker_compose || exit 1
test_ports
test_files
test_directories
test_permissions
test_environment
test_docker_build || exit 1

show_system_info
show_next_steps

echo ""
echo "========================================"
echo -e "${GREEN}✅ Pre-flight checks completed!${NC}"
echo "========================================"

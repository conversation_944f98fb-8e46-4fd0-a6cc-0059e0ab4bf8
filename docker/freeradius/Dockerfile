FROM ubuntu:22.04

# Install dependencies
RUN apt-get update && apt-get install -y \
    freeradius \
    freeradius-mysql \
    freeradius-utils \
    mysql-client \
    vim \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create directories
RUN mkdir -p /var/log/freeradius \
    && chown -R freerad:freerad /var/log/freeradius \
    && chmod -R 755 /var/log/freeradius

# Copy configuration files
COPY raddb/ /etc/freeradius/3.0/
COPY scripts/ /opt/scripts/

# Set permissions
RUN chown -R freerad:freerad /etc/freeradius/3.0/ \
    && chmod -R 640 /etc/freeradius/3.0/ \
    && chmod +x /opt/scripts/*.sh

# Expose ports
EXPOSE 1812/udp 1813/udp 3799/udp

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD radtest test test localhost 1812 testing123 || exit 1

# Start FreeRADIUS
CMD ["/opt/scripts/start.sh"]

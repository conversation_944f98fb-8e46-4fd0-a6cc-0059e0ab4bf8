version: '3.8'

services:
  # Web Server (Nginx)
  nginx:
    image: nginx:alpine
    container_name: frradius_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./public:/var/www/html/public
      - ./docker/nginx/nginx.conf:/etc/nginx/conf.d/default.conf
      - ./storage/logs:/var/log/nginx
    depends_on:
      - php
    networks:
      - frradius_network

  # PHP-FPM
  php:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
    container_name: frradius_php
    volumes:
      - .:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=mysql_main
      - DB_PORT=3306
      - DB_DATABASE=frradius_main
      - DB_USERNAME=frradius_user
      - DB_PASSWORD=frradius_password
      - RADIUS_HOST=mysql_radius
      - RADIUS_PORT=3306
      - RADIUS_DATABASE=frradius_auth
      - RADIUS_USERNAME=frradius_user
      - RADIUS_PASSWORD=frradius_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - IP_RADIUS_SERVER=freeradius
    depends_on:
      - mysql_main
      - mysql_radius
      - redis
      - freeradius
    networks:
      - frradius_network

  # MySQL Main Database
  mysql_main:
    image: mysql:8.0
    container_name: frradius_mysql_main
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: frradius_main
      MYSQL_USER: frradius_user
      MYSQL_PASSWORD: frradius_password
    volumes:
      - mysql_main_data:/var/lib/mysql
      - ./docker/mysql/main.cnf:/etc/mysql/conf.d/custom.cnf
      - ./docker/mysql/init-main.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - frradius_network

  # MySQL RADIUS Database
  mysql_radius:
    image: mysql:8.0
    container_name: frradius_mysql_radius
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: frradius_auth
      MYSQL_USER: frradius_user
      MYSQL_PASSWORD: frradius_password
    volumes:
      - mysql_radius_data:/var/lib/mysql
      - ./docker/mysql/radius.cnf:/etc/mysql/conf.d/custom.cnf
      - ./docker/mysql/init-radius.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3307:3306"
    networks:
      - frradius_network

  # Redis
  redis:
    image: redis:alpine
    container_name: frradius_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - frradius_network

  # PhpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: frradius_phpmyadmin
    environment:
      PMA_ARBITRARY: 1
      PMA_HOST: mysql_main
      PMA_USER: frradius_user
      PMA_PASSWORD: frradius_password
    ports:
      - "8080:80"
    depends_on:
      - mysql_main
      - mysql_radius
    networks:
      - frradius_network

  # FreeRADIUS Server
  freeradius:
    build:
      context: ./docker/freeradius
      dockerfile: Dockerfile
    container_name: frradius_freeradius
    ports:
      - "1812:1812/udp"  # Authentication
      - "1813:1813/udp"  # Accounting
      - "3799:3799/udp"  # Dynamic Authorization
    volumes:
      - ./docker/freeradius/raddb:/etc/freeradius/3.0
      - ./docker/freeradius/logs:/var/log/freeradius
    environment:
      - MYSQL_HOST=mysql_radius
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=frradius_auth
      - MYSQL_USERNAME=frradius_user
      - MYSQL_PASSWORD=frradius_password
    depends_on:
      - mysql_radius
    networks:
      - frradius_network

  # WhatsApp Gateway
  whatsapp_gateway:
    build:
      context: ./docker/whatsapp
      dockerfile: Dockerfile
    container_name: frradius_whatsapp
    ports:
      - "3000:3000"
    volumes:
      - ./docker/whatsapp/sessions:/app/sessions
    environment:
      - NODE_ENV=production
      - PORT=3000
    networks:
      - frradius_network

  # Node.js for asset compilation
  node:
    image: node:18-alpine
    container_name: frradius_node
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
    command: sh -c "npm install && npm run dev"
    networks:
      - frradius_network

volumes:
  mysql_main_data:
  mysql_radius_data:
  redis_data:

networks:
  frradius_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

APP_NAME=FRRadius
APP_ENV=production
APP_KEY=base64:yudZwm4eXbI3znTsGGAgdBEq2NShCcvJPmBNODMsoeg=
APP_DEBUG=false
APP_TIMEZONE=Asia/Jakarta
APP_URL=http://localhost

# RADIUS Server Configuration
IP_RADIUS_SERVER=freeradius
IP_RADIUS_USERNAME=frradius_user
IP_WEB_ISOLIR=***********

# Midtrans Configuration (keep your existing values)
MERCHANT_MIDTRANS=G132706530
SERVER_MIDTRANS=Mid-server-5iI_TXQPqwt0aoYBnltvNpjM
CLIENT_MIDTRANS=Mid-client-eHid7cD1W-D12dgk
STATUS_MIDTRANS=Production

APP_LOCALE=id
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
PHP_CLI_SERVER_WORKERS=4
BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Main Database Configuration
DB_CONNECTION=mysql
DB_HOST=mysql_main
DB_PORT=3306
DB_DATABASE=frradius_main
DB_USERNAME=frradius_user
DB_PASSWORD=frradius_password

# RADIUS Database Configuration
RADIUS_HOST=mysql_radius
RADIUS_PORT=3306
RADIUS_DATABASE=frradius_auth
RADIUS_USERNAME=frradius_user
RADIUS_PASSWORD=frradius_password

# Session Configuration
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local

# Cache Configuration
CACHE_STORE=redis
CACHE_PREFIX=

# Queue Configuration
QUEUE_CONNECTION=redis

# Redis Configuration
REDIS_CLIENT=phpredis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS Configuration
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# WhatsApp Gateway Configuration
WHATSAPP_GATEWAY_URL=http://whatsapp_gateway:3000

VITE_APP_NAME="${APP_NAME}"

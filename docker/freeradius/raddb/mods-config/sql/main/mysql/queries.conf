# -*- text -*-
# main/mysql/queries.conf -- MySQL configuration for default schema (schema.sql)
#
# $Id: 8c4b5b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b8b $

# Safe characters list for sql queries. Everything else is replaced
# with their mime-encoded equivalents.
# The default list should be ok
# safe_characters = "@abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789.-_: /"

#######################################################################
#  Connection config
#######################################################################
# The character set is not configurable. The default character set of
# the mysql client library is used. To control the character set,
# create/edit my.cnf file and set "default-character-set = utf8" in
# the "[mysql]" section.

#######################################################################
#  Query config:  Username
#######################################################################
# This is the username that will get substituted, escaped, and added
# as attribute 'SQL-User-Name'. '%{SQL-User-Name}' should be used below
# everywhere a username substitution is needed so you you can be sure
# the username passed from the client is escaped properly.
#
# Uncomment the next line, if you want the sql_user_name to mean:
#
#    Use Stripped-User-Name, if it's there.
#    Else use User-Name, if it's there,
#    Else use hard-coded string "DEFAULT" as the user name.
#sql_user_name = "%{%{Stripped-User-Name}:-%{%{User-Name}:-DEFAULT}}"
#
sql_user_name = "%{User-Name}"

#######################################################################
#  Default profile
#######################################################################
# This is the default profile. It is found in SQL by group membership.
# That means that this profile must be a member of at least one group
# which will contain the corresponding check and reply items.
# This profile will be queried in the authorize section for every user.
# The point is to assign all users a default profile without having to
# manually add each one to a group that will contain the default profile.

# The SQL module will replace %{config:default_profile} with the
# default_profile from the configuration file.
# To disable the default profile, leave the configuration file blank.

# If you are not using the default profile, delete this query.
default_profile = ""

#######################################################################
#  NAS Query
#######################################################################
# This query retrieves the radius clients.
#
# 0. Row ID (currently unused)
# 1. Name (or IP address)
# 2. Shortname
# 3. Type
# 4. Secret
# 5. Server
client_query = "SELECT id, nasname, shortname, type, secret, server FROM ${client_table}"

#######################################################################
#  Authorization Queries
#######################################################################
# These queries compare the check items for the user
# in ${authcheck_table} and setup the reply items in
# ${authreply_table}. You can use any query/tables
# you want, but the return data for each row MUST
# be in the following order:
#
# 0. Row ID (currently unused)
# 1. UserName/GroupName
# 2. Item Attr Name
# 3. Item Attr Value
# 4. Item Attr Operation
#######################################################################
# Use these for case sensitive usernames.
authorize_check_query = "SELECT id, username, attribute, value, op \
  FROM ${authcheck_table} \
  WHERE username = '%{SQL-User-Name}' \
  ORDER BY id"

authorize_reply_query = "SELECT id, username, attribute, value, op \
  FROM ${authreply_table} \
  WHERE username = '%{SQL-User-Name}' \
  ORDER BY id"

# Use these for case insensitive usernames. WARNING: Slower queries!
# authorize_check_query = "SELECT id, username, attribute, value, op \
#   FROM ${authcheck_table} \
#   WHERE LOWER(username) = LOWER('%{SQL-User-Name}') \
#   ORDER BY id"

# authorize_reply_query = "SELECT id, username, attribute, value, op \
#   FROM ${authreply_table} \
#   WHERE LOWER(username) = LOWER('%{SQL-User-Name}') \
#   ORDER BY id"

authorize_group_check_query = "SELECT id, groupname, attribute, \
  Value, op \
  FROM ${groupcheck_table} \
  WHERE groupname = '%{Sql-Group}' \
  ORDER BY id"

authorize_group_reply_query = "SELECT id, groupname, attribute, \
  value, op \
  FROM ${groupreply_table} \
  WHERE groupname = '%{Sql-Group}' \
  ORDER BY id"

#######################################################################
# Authentication Queries
#######################################################################
# These queries compare the check items for the user
# in ${authcheck_table} and setup the reply items in
# ${authreply_table}. You can use any query/tables
# you want, but the return data for each row MUST
# be in the following order:
#
# 0. Row ID (currently unused)
# 1. UserName/GroupName
# 2. Item Attr Name
# 3. Item Attr Value
# 4. Item Attr Operation
#######################################################################

authenticate_check_query = "SELECT id, username, attribute, value, op \
  FROM ${authcheck_table} \
  WHERE username = '%{SQL-User-Name}' \
  ORDER BY id"

authenticate_reply_query = "SELECT id, username, attribute, value, op \
  FROM ${authreply_table} \
  WHERE username = '%{SQL-User-Name}' \
  ORDER BY id"

#######################################################################
# Group Membership Queries
#######################################################################
# group_membership_query        - Check user group membership
#######################################################################

# Use these for case sensitive usernames.
group_membership_query = "SELECT groupname \
  FROM ${usergroup_table} \
  WHERE username = '%{SQL-User-Name}' \
  ORDER BY priority"

# Use these for case insensitive usernames. WARNING: Slower queries!
# group_membership_query = "SELECT groupname \
#   FROM ${usergroup_table} \
#   WHERE LOWER(username) = LOWER('%{SQL-User-Name}') \
#   ORDER BY priority"

#######################################################################
# Accounting and Post-Auth Queries
#######################################################################
# These queries insert/update records in the accounting table.
#######################################################################

accounting_onoff_query = "\
  UPDATE ${acct_table1} \
  SET \
    acctstoptime       =  '%S', \
    acctsessiontime    =  unix_timestamp('%S') - \
                          unix_timestamp(acctstarttime), \
    acctinputoctets    =  '%{%{Acct-Input-Gigawords}:-0}' << 32 | \
                          '%{%{Acct-Input-Octets}:-0}', \
    acctoutputoctets   =  '%{%{Acct-Output-Gigawords}:-0}' << 32 | \
                          '%{%{Acct-Output-Octets}:-0}', \
    acctterminatecause =  '%{Acct-Terminate-Cause}' \
  WHERE acctstoptime IS NULL \
  AND nasipaddress      =  '%{NAS-IP-Address}' \
  AND acctstarttime     <= '%S'"

accounting_update_query = " \
  UPDATE ${acct_table1} SET \
    framedipaddress = '%{Framed-IP-Address}', \
    acctsessiontime = '%{Acct-Session-Time}', \
    acctinputoctets = '%{%{Acct-Input-Gigawords}:-0}' << 32 | \
                      '%{%{Acct-Input-Octets}:-0}', \
    acctoutputoctets = '%{%{Acct-Output-Gigawords}:-0}' << 32 | \
                       '%{%{Acct-Output-Octets}:-0}' \
  WHERE acctsessionid = '%{Acct-Session-Id}' \
  AND username = '%{SQL-User-Name}' \
  AND nasipaddress = '%{NAS-IP-Address}'"

accounting_update_query_alt = " \
  INSERT INTO ${acct_table1} \
    (acctsessionid,    acctuniqueid,      username, \
     realm,            nasipaddress,      nasportid, \
     nasporttype,      acctstarttime,     acctupdatetime, \
     acctstoptime,     acctsessiontime,   acctauthentic, \
     connectinfo_start, connectinfo_stop, acctinputoctets, \
     acctoutputoctets, calledstationid,   callingstationid, \
     acctterminatecause, servicetype,     framedprotocol, \
     framedipaddress) \
  VALUES \
    ('%{Acct-Session-Id}', '%{Acct-Unique-Session-Id}', \
     '%{SQL-User-Name}', \
     '%{Realm}', '%{NAS-IP-Address}', '%{NAS-Port}', \
     '%{NAS-Port-Type}', \
     DATE_SUB('%S', \
              INTERVAL (%{%{Acct-Session-Time}:-0} + \
                        %{%{Acct-Delay-Time}:-0}) SECOND), \
     '%S', NULL, '%{Acct-Session-Time}', '%{Acct-Authentic}', \
     '%{Connect-Info}', '', \
     '%{%{Acct-Input-Gigawords}:-0}' << 32 | \
     '%{%{Acct-Input-Octets}:-0}', \
     '%{%{Acct-Output-Gigawords}:-0}' << 32 | \
     '%{%{Acct-Output-Octets}:-0}', \
     '%{Called-Station-Id}', '%{Calling-Station-Id}', \
     '%{Acct-Terminate-Cause}', '%{Service-Type}', \
     '%{Framed-Protocol}', '%{Framed-IP-Address}') \
  ON DUPLICATE KEY UPDATE \
     acctupdatetime     = '%S', \
     acctsessiontime    = '%{Acct-Session-Time}', \
     acctinputoctets    = '%{%{Acct-Input-Gigawords}:-0}' << 32 | \
                          '%{%{Acct-Input-Octets}:-0}', \
     acctoutputoctets   = '%{%{Acct-Output-Gigawords}:-0}' << 32 | \
                          '%{%{Acct-Output-Octets}:-0}'"

accounting_start_query = " \
  INSERT INTO ${acct_table1} \
    (acctsessionid,    acctuniqueid,     username, \
     realm,            nasipaddress,     nasportid, \
     nasporttype,      acctstarttime,    acctupdatetime, \
     acctstoptime,     acctsessiontime,  acctauthentic, \
     connectinfo_start, connectinfo_stop, acctinputoctets, \
     acctoutputoctets, calledstationid,  callingstationid, \
     acctterminatecause, servicetype,    framedprotocol, \
     framedipaddress) \
  VALUES \
    ('%{Acct-Session-Id}', '%{Acct-Unique-Session-Id}', \
     '%{SQL-User-Name}', \
     '%{Realm}', '%{NAS-IP-Address}', '%{NAS-Port}', \
     '%{NAS-Port-Type}', '%S', '%S', NULL, \
     '0', '%{Acct-Authentic}', '%{Connect-Info}', '', \
     '0', '0', '%{Called-Station-Id}', '%{Calling-Station-Id}', \
     '', '%{Service-Type}', '%{Framed-Protocol}', \
     '%{Framed-IP-Address}')"

accounting_start_query_alt  = " \
  UPDATE ${acct_table1} SET \
     acctstarttime     = '%S', \
     acctupdatetime    = '%S', \
     connectinfo_start = '%{Connect-Info}' \
  WHERE acctsessionid  = '%{Acct-Session-Id}' \
  AND username         = '%{SQL-User-Name}' \
  AND nasipaddress     = '%{NAS-IP-Address}'"

accounting_stop_query = " \
  UPDATE ${acct_table2} SET \
    acctstoptime       = '%S', \
    acctsessiontime    = '%{Acct-Session-Time}', \
    acctinputoctets    = '%{%{Acct-Input-Gigawords}:-0}' << 32 | \
                         '%{%{Acct-Input-Octets}:-0}', \
    acctoutputoctets   = '%{%{Acct-Output-Gigawords}:-0}' << 32 | \
                         '%{%{Acct-Output-Octets}:-0}', \
    acctterminatecause = '%{Acct-Terminate-Cause}', \
    connectinfo_stop   = '%{Connect-Info}' \
  WHERE acctsessionid   = '%{Acct-Session-Id}' \
  AND username          = '%{SQL-User-Name}' \
  AND nasipaddress      = '%{NAS-IP-Address}'"

accounting_stop_query_alt = " \
  INSERT INTO ${acct_table2} \
    (acctsessionid, acctuniqueid, username, \
     realm, nasipaddress, nasportid, \
     nasporttype, acctstarttime, acctstoptime, \
     acctsessiontime, acctauthentic, connectinfo_start, \
     connectinfo_stop, acctinputoctets, acctoutputoctets, \
     calledstationid, callingstationid, acctterminatecause, \
     servicetype, framedprotocol, framedipaddress) \
  VALUES \
    ('%{Acct-Session-Id}', '%{Acct-Unique-Session-Id}', \
     '%{SQL-User-Name}', \
     '%{Realm}', '%{NAS-IP-Address}', '%{NAS-Port}', \
     '%{NAS-Port-Type}', \
     DATE_SUB('%S', INTERVAL (%{%{Acct-Session-Time}:-0} + \
                               %{%{Acct-Delay-Time}:-0}) SECOND), \
     '%S', '%{Acct-Session-Time}', '%{Acct-Authentic}', '', \
     '%{Connect-Info}', \
     '%{%{Acct-Input-Gigawords}:-0}' << 32 | \
     '%{%{Acct-Input-Octets}:-0}', \
     '%{%{Acct-Output-Gigawords}:-0}' << 32 | \
     '%{%{Acct-Output-Octets}:-0}', \
     '%{Called-Station-Id}', '%{Calling-Station-Id}', \
     '%{Acct-Terminate-Cause}', '%{Service-Type}', \
     '%{Framed-Protocol}', '%{Framed-IP-Address}') \
  ON DUPLICATE KEY UPDATE \
     acctstoptime      = '%S', \
     acctsessiontime   = '%{Acct-Session-Time}', \
     connectinfo_stop  = '%{Connect-Info}', \
     acctinputoctets   = '%{%{Acct-Input-Gigawords}:-0}' << 32 | \
                         '%{%{Acct-Input-Octets}:-0}', \
     acctoutputoctets  = '%{%{Acct-Output-Gigawords}:-0}' << 32 | \
                         '%{%{Acct-Output-Octets}:-0}', \
     acctterminatecause = '%{Acct-Terminate-Cause}'"

#######################################################################
# Simultaneous Use Checking Queries
#######################################################################
# simul_count_query     - query for the number of current connections
# simul_verify_query    - query to return the current connections for verification
# Uncomment simul_count_query to enable simultaneous use checking
#######################################################################
# simul_count_query = "SELECT COUNT(*) \
#                      FROM ${acct_table1} \
#                      WHERE username = '%{SQL-User-Name}' \
#                      AND acctstoptime IS NULL"

# simul_verify_query = "SELECT \
#                         radacctid, acctsessionid, username, nasipaddress, nasportid, framedipaddress, \
#                         callingstationid, framedprotocol \
#                       FROM ${acct_table1} \
#                       WHERE username = '%{SQL-User-Name}' \
#                       AND acctstoptime IS NULL"

#######################################################################
# Post-Auth Query
# This query is run after a successful authentication
#######################################################################
post-auth_query = "INSERT INTO ${postauth_table} \
  (username, pass, reply, authdate) \
  VALUES ( \
  '%{SQL-User-Name}', \
  '%{%{User-Password}:-%{Chap-Password}}', \
  '%{reply:Packet-Type}', '%S')"

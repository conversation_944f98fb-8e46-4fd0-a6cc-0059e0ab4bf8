FROM node:18-alpine

# Install dependencies
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    && rm -rf /var/cache/apk/*

# Tell Puppeteer to skip installing Chromium. We'll be using the installed package.
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install app dependencies
RUN npm ci --only=production && npm cache clean --force

# Create a user to run the app
RUN addgroup -g 1001 -S nodejs && \
    adduser -S whatsapp -u 1001

# Copy app source
COPY . .

# Create sessions directory
RUN mkdir -p sessions && chown -R whatsapp:nodejs sessions

# Change to non-root user
USER whatsapp

# Expose port
EXPOSE 3000

# Start the application
CMD ["node", "server.js"]

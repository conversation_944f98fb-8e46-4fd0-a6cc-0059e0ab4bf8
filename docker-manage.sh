#!/bin/bash

# FRRadius Docker Management Script

show_help() {
    echo "🐳 FRRadius Docker Management"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start       Start all services"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  status      Show service status"
    echo "  logs        Show logs (use -f for follow)"
    echo "  shell       Access PHP container shell"
    echo "  mysql       Access MySQL main database"
    echo "  radius-db   Access RADIUS database"
    echo "  backup      Backup databases"
    echo "  restore     Restore databases"
    echo "  update      Update and rebuild containers"
    echo "  clean       Clean up unused containers and images"
    echo "  reset       Reset everything (DANGER: removes all data)"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs -f nginx"
    echo "  $0 shell"
    echo ""
}

start_services() {
    echo "🚀 Starting FRRadius services..."
    docker-compose up -d
    echo "✅ Services started!"
    docker-compose ps
}

stop_services() {
    echo "🛑 Stopping FRRadius services..."
    docker-compose down
    echo "✅ Services stopped!"
}

restart_services() {
    echo "🔄 Restarting FRRadius services..."
    docker-compose restart
    echo "✅ Services restarted!"
    docker-compose ps
}

show_status() {
    echo "📊 FRRadius Service Status:"
    docker-compose ps
    echo ""
    echo "🔍 Resource Usage:"
    docker stats --no-stream
}

show_logs() {
    if [ "$2" = "-f" ]; then
        if [ -n "$3" ]; then
            docker-compose logs -f "$3"
        else
            docker-compose logs -f
        fi
    else
        if [ -n "$2" ]; then
            docker-compose logs "$2"
        else
            docker-compose logs
        fi
    fi
}

access_shell() {
    echo "🐚 Accessing PHP container shell..."
    docker-compose exec php bash
}

access_mysql() {
    echo "🗄️  Accessing MySQL main database..."
    docker-compose exec mysql_main mysql -u frradius_user -pfrradius_password frradius_main
}

access_radius_db() {
    echo "🗄️  Accessing RADIUS database..."
    docker-compose exec mysql_radius mysql -u frradius_user -pfrradius_password frradius_auth
}

backup_databases() {
    echo "💾 Backing up databases..."
    
    # Create backup directory
    mkdir -p backups/$(date +%Y%m%d_%H%M%S)
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    
    # Backup main database
    echo "📦 Backing up main database..."
    docker-compose exec mysql_main mysqldump -u frradius_user -pfrradius_password frradius_main > "$BACKUP_DIR/frradius_main.sql"
    
    # Backup RADIUS database
    echo "📦 Backing up RADIUS database..."
    docker-compose exec mysql_radius mysqldump -u frradius_user -pfrradius_password frradius_auth > "$BACKUP_DIR/frradius_auth.sql"
    
    # Backup application files
    echo "📦 Backing up application files..."
    tar -czf "$BACKUP_DIR/app_files.tar.gz" storage/ public/storage/ .env
    
    echo "✅ Backup completed in $BACKUP_DIR"
}

restore_databases() {
    echo "⚠️  Database Restore"
    echo "Available backups:"
    ls -la backups/
    echo ""
    read -p "Enter backup directory name (e.g., 20231201_143000): " BACKUP_DIR
    
    if [ ! -d "backups/$BACKUP_DIR" ]; then
        echo "❌ Backup directory not found!"
        exit 1
    fi
    
    echo "🔄 Restoring databases from backups/$BACKUP_DIR..."
    
    # Restore main database
    if [ -f "backups/$BACKUP_DIR/frradius_main.sql" ]; then
        echo "📥 Restoring main database..."
        docker-compose exec -T mysql_main mysql -u frradius_user -pfrradius_password frradius_main < "backups/$BACKUP_DIR/frradius_main.sql"
    fi
    
    # Restore RADIUS database
    if [ -f "backups/$BACKUP_DIR/frradius_auth.sql" ]; then
        echo "📥 Restoring RADIUS database..."
        docker-compose exec -T mysql_radius mysql -u frradius_user -pfrradius_password frradius_auth < "backups/$BACKUP_DIR/frradius_auth.sql"
    fi
    
    # Restore application files
    if [ -f "backups/$BACKUP_DIR/app_files.tar.gz" ]; then
        echo "📥 Restoring application files..."
        tar -xzf "backups/$BACKUP_DIR/app_files.tar.gz"
    fi
    
    echo "✅ Restore completed!"
}

update_containers() {
    echo "🔄 Updating FRRadius containers..."
    
    # Pull latest images
    docker-compose pull
    
    # Rebuild containers
    docker-compose build --no-cache
    
    # Restart services
    docker-compose down
    docker-compose up -d
    
    # Run Laravel updates
    docker-compose exec php composer install --no-dev --optimize-autoloader
    docker-compose exec php php artisan migrate --force
    docker-compose exec php php artisan config:cache
    docker-compose exec php php artisan route:cache
    docker-compose exec php php artisan view:cache
    
    echo "✅ Update completed!"
}

clean_docker() {
    echo "🧹 Cleaning up Docker..."
    
    # Remove unused containers
    docker container prune -f
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    # Remove unused networks
    docker network prune -f
    
    echo "✅ Cleanup completed!"
}

reset_everything() {
    echo "⚠️  DANGER: This will remove ALL data!"
    echo "This includes:"
    echo "- All containers"
    echo "- All volumes (databases will be lost)"
    echo "- All networks"
    echo ""
    read -p "Are you sure? Type 'YES' to continue: " CONFIRM
    
    if [ "$CONFIRM" = "YES" ]; then
        echo "🗑️  Removing everything..."
        docker-compose down -v --remove-orphans
        docker system prune -a -f --volumes
        echo "✅ Reset completed!"
        echo "Run './docker-setup.sh' to reinstall."
    else
        echo "❌ Reset cancelled."
    fi
}

# Main script logic
case "$1" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$@"
        ;;
    shell)
        access_shell
        ;;
    mysql)
        access_mysql
        ;;
    radius-db)
        access_radius_db
        ;;
    backup)
        backup_databases
        ;;
    restore)
        restore_databases
        ;;
    update)
        update_containers
        ;;
    clean)
        clean_docker
        ;;
    reset)
        reset_everything
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac

{"name": "frradius-whatsapp-gateway", "version": "1.0.0", "description": "WhatsApp Gateway for FRRadius", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "whatsapp-web.js": "^1.21.0", "qrcode": "^1.5.3", "cors": "^2.8.5", "body-parser": "^1.20.2", "axios": "^1.4.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["whatsapp", "gateway", "api", "f<PERSON><PERSON>"], "author": "FRRadius Team", "license": "MIT"}
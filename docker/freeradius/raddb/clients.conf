# FreeRADIUS Clients Configuration for FRRadius

# Local testing client
client localhost {
    ipaddr = 127.0.0.1
    secret = testing123
    require_message_authenticator = no
    nas_type = other
    shortname = localhost
}

# Docker network
client docker_network {
    ipaddr = **********/16
    secret = frradius
    require_message_authenticator = no
    nas_type = other
    shortname = docker
}

# Allow all Mikrotik routers (you should restrict this in production)
client mikrotik_routers {
    ipaddr = 0.0.0.0/0
    secret = frradius
    require_message_authenticator = no
    nas_type = mikrotik
    shortname = mikrotik
}

# OLT devices (ZTE, Huawei, etc.)
client olt_devices {
    ipaddr = 0.0.0.0/0
    secret = frradius
    require_message_authenticator = no
    nas_type = other
    shortname = olt
}

# You can add specific client configurations here
# Example for a specific router:
# client router1 {
#     ipaddr = ***********
#     secret = your_secret_here
#     require_message_authenticator = no
#     nas_type = mikrotik
#     shortname = router1
# }

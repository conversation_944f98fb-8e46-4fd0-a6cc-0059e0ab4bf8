[mysqld]
# Basic settings
default-storage-engine = innodb
innodb_file_per_table = 1
innodb_flush_log_at_trx_commit = 2
innodb_log_buffer_size = 32M
innodb_buffer_pool_size = 256M
innodb_log_file_size = 128M
innodb_thread_concurrency = 16
innodb_autoextend_increment = 64
innodb_buffer_pool_instances = 8
innodb_concurrency_tickets = 5000
innodb_old_blocks_time = 1000
innodb_stats_on_metadata = 0
innodb_file_format = Barracuda

# Connection settings
max_connections = 200
max_connect_errors = 1000000
max_allowed_packet = 256M
interactive_timeout = 3600
wait_timeout = 3600

# Query cache
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 2M

# Logging
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Character set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Timezone
default-time-zone = '+07:00'

# Binary logging
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
